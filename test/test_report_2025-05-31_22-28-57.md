# API System Test Report

**Date:** 2025-05-31 22:28:57
**PHP Version:** 8.4.6
**Total Tests:** 280
**Passed:** 260
**Failed:** 20
**Success Rate:** 92.86%

## Test Results by Class

### SessionAuthTest - ✅ PASSED

- **Total Tests:** 15
- **Passed:** 15
- **Failed:** 0

### AgenciesApiTest - ✅ PASSED

- **Total Tests:** 30
- **Passed:** 30
- **Failed:** 0

### UsersApiTest - ✅ PASSED

- **Total Tests:** 23
- **Passed:** 23
- **Failed:** 0

### PatientsApiTest - ❌ FAILED

- **Total Tests:** 25
- **Passed:** 21
- **Failed:** 4

#### Failed Tests:

- Should fail when editing non-existent patient
- Response should contain error message (Key 'error' not found in array)
- Should fail when deleting non-existent patient
- Response should contain error message (Key 'error' not found in array)

### SurgeriesApiTest - ❌ FAILED

- **Total Tests:** 24
- **Passed:** 19
- **Failed:** 5

#### Failed Tests:

- Should successfully add surgery with valid data
- Response should contain surgery ID (Key 'id' not found in array)
- No test surgery ID available for get test
- No test surgery ID available for edit test
- No test surgery ID available for delete test

### RoomsApiTest - ✅ PASSED

- **Total Tests:** 26
- **Passed:** 26
- **Failed:** 0

### TechniciansApiTest - ✅ PASSED

- **Total Tests:** 23
- **Passed:** 23
- **Failed:** 0

### PhotosApiTest - ✅ PASSED

- **Total Tests:** 20
- **Passed:** 20
- **Failed:** 0

### ReservationsApiTest - ❌ FAILED

- **Total Tests:** 26
- **Passed:** 22
- **Failed:** 4

#### Failed Tests:

- Missing test data for reservation test
- No test reservation ID available for get test
- No test reservation ID available for update test
- No test reservation ID available for cancel test

### AvailabilityApiTest - ✅ PASSED

- **Total Tests:** 23
- **Passed:** 23
- **Failed:** 0

### TechAvailApiTest - ✅ PASSED

- **Total Tests:** 30
- **Passed:** 30
- **Failed:** 0

### EnhancedSurgeryBookingTest - ❌ FAILED

- **Total Tests:** 15
- **Passed:** 8
- **Failed:** 7

#### Failed Tests:

- Should have at least 3 available technicians
- Insufficient test data for surgery booking test
- No test surgery available for status automation test
- Insufficient test data for room reservation test
- Insufficient test data for technician validation test
- No test surgery available for edit test
- No test surgery available for deletion test

