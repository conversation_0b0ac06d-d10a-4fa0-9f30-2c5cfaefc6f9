# API System Test Report

**Date:** 2025-05-31 22:31:55
**PHP Version:** 8.4.6
**Total Tests:** 292
**Passed:** 284
**Failed:** 8
**Success Rate:** 97.26%

## Test Results by Class

### SessionAuthTest - ✅ PASSED

- **Total Tests:** 15
- **Passed:** 15
- **Failed:** 0

### AgenciesApiTest - ✅ PASSED

- **Total Tests:** 30
- **Passed:** 30
- **Failed:** 0

### UsersApiTest - ✅ PASSED

- **Total Tests:** 23
- **Passed:** 23
- **Failed:** 0

### PatientsApiTest - ❌ FAILED

- **Total Tests:** 25
- **Passed:** 21
- **Failed:** 4

#### Failed Tests:

- Should fail when editing non-existent patient
- Response should contain error message (Key 'error' not found in array)
- Should fail when deleting non-existent patient
- Response should contain error message (Key 'error' not found in array)

### SurgeriesApiTest - ✅ PASSED

- **Total Tests:** 25
- **Passed:** 25
- **Failed:** 0

### RoomsApiTest - ✅ PASSED

- **Total Tests:** 26
- **Passed:** 26
- **Failed:** 0

### TechniciansApiTest - ✅ PASSED

- **Total Tests:** 23
- **Passed:** 23
- **Failed:** 0

### PhotosApiTest - ✅ PASSED

- **Total Tests:** 20
- **Passed:** 20
- **Failed:** 0

### ReservationsApiTest - ❌ FAILED

- **Total Tests:** 26
- **Passed:** 22
- **Failed:** 4

#### Failed Tests:

- Missing test data for reservation test
- No test reservation ID available for get test
- No test reservation ID available for update test
- No test reservation ID available for cancel test

### AvailabilityApiTest - ✅ PASSED

- **Total Tests:** 23
- **Passed:** 23
- **Failed:** 0

### TechAvailApiTest - ✅ PASSED

- **Total Tests:** 30
- **Passed:** 30
- **Failed:** 0

### EnhancedSurgeryBookingTest - ✅ PASSED

- **Total Tests:** 26
- **Passed:** 26
- **Failed:** 0

