.calendar-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.calendar-header {
    background: linear-gradient(135deg, rgb(88, 88, 89) 0%, rgb(55, 78, 102) 50%);
    color: white;
    padding: 0.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.calendar-nav {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.calendar-nav button {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    /* padding: 0.5rem 0.75rem; */
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
}

.calendar-nav button:hover {
    background: rgba(255, 255, 255, 0.3);
}

.calendar-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
}

.view-toggle {
    display: flex;
    gap: 0.25rem;
}

.view-toggle button {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    /* padding: 0.5rem 0.75rem; */
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 0.875rem;
}

.view-toggle button.active {
    background: white;
    color: #0d6efd;
}

.view-toggle button:hover:not(.active) {
    background: rgba(255, 255, 255, 0.3);
}

/* Calendar Grid */
.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    background: #e9ecef;
}

.calendar-day-header {
    background: #f0f0f0;
    padding: 0.75rem 0.5rem;
    text-align: center;
    font-weight: 600;
    color: #495057;
    font-size: 0.875rem;
}

.calendar-day {
    background: white;
    min-height: 120px;
    padding: 0.5rem;
    position: relative;
    border: 1px solid transparent;
    transition: all 0.2s;
}

.calendar-day:hover {
    background: #f8f9fa;
    border-color: #dee2e6;
}

.calendar-day.other-month {
    background: #f8f9fa;
    color: #6c757d;
}

.calendar-day.today {
    background: #e3f2fd;
    border-color: #2196f3;
}

.day-number {
    font-weight: 600;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

/* Room Slots */
.room-slots {
    display: flex;
    flex-direction: column;
    gap: 3px;
    margin-bottom: 4px;
}

.room-slot-container {
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
    border: 1px solid transparent;
    font-size: 0.7rem;
    position: relative;
    min-height: 35px;
    padding: 3px;
}

.room-slot-container.available {
    /* background: #d4edda; */
    border-color: #c3e6cb;
}

.room-slot-container.available:hover {
    background: #c3e6cb;
    transform: scale(1.02);
}

.room-slot-container.booked {
    background: #fff3cd;
    border-color: #ffeaa7;
}

.room-slot-container.booked:hover {
    background: #ffeaa7;
    transform: scale(1.02);
}

.room-badge {
    position: absolute;
    top: 2px;
    left: 2px;
    /* background: #0d6efd; */
    color: #a4a4a4;
    padding: 1px 4px;
    border-radius: 2px;
    font-size: 0.6rem;
    font-weight: 600;
    z-index: 1;
}

.room-badge.available {
    /* background: #28a745; */
}

.room-badge.booked {
    background: #dc3545;
    color: white;
}

/* Not Available Slot Styling */
.not-available-slot {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    min-height: 25px;
    padding-top: 8px;
}

.not-available-text {
    font-size: 0.65rem;
    font-weight: 600;
    color: #6c757d;
    text-align: center;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}
.surgery-content {
    padding-top: 15px;
    padding-left: 2px;
    padding-right: 2px;
}

.surgery-header {
    display: flex;
    align-items: center;
    gap: 3px;
    margin-bottom: 2px;
}

.patient-name {
    font-weight: 600;
    font-size: 0.65rem;
    color: #212529;
}

.graft-count {
    font-size: 0.6rem;
    color: #6c757d;
    margin-bottom: 1px;
}

.technician-names {
    font-size: 0.55rem;
    color: #495057;
    margin-bottom: 1px;
}

.agency-name {
    font-size: 0.55rem;
    color: #6c757d;
    font-style: italic;
}

.empty-slot {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    min-height: 25px;
    padding-top: 10px;
}

.add-surgery-text {
    color: #000000;
    font-size: 1rem;
    font-weight: 500;
    text-align: center;
}

/* Appointment Summary */
.appointment-summary {
    padding-top: 15px;
    padding-left: 2px;
    padding-right: 2px;
}

.appointment-type {
    font-size: 0.6rem;
    margin-bottom: 2px;
    padding: 1px 0;
    font-weight: 500;
    display: flex;
    align-items: center;
    color: #495057;
}

.appointment-type::before {
    content: '';
    width: 6px;
    height: 6px;
    border-radius: 1px;
    margin-right: 4px;
    flex-shrink: 0;
}

.appointment-type.consult::before {
    background: #0d6efd;
}

.appointment-type.cosmetic::before {
    background: #198754;
}

.appointment-type.surgery::before {
    background: #dc3545;
}
.day-events {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.event-item {
    display: flex;
    flex-direction: column;
    /* Stack children vertically */
    color: #212529;
    /* Change text color to dark for better contrast with badge */
    background: #e9ecef;
    /* Add a light background to the event item */
    padding: 2px 0;
    /* Remove horizontal padding from the item itself */
    border-radius: 3px;
    font-size: 0.75rem;
    text-decoration: none;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    transition: all 0.2s;
}

.event-item>div {
    /* Target direct child divs (event-header and graft-count) */
    padding-left: 6px;
    /* Add left padding to the content inside */
    padding-right: 6px;
    /* Add right padding to the content inside */
}

.event-item:hover {
    background: #dee2e6;
    /* Lighter hover background */
    color: #212529;
    text-decoration: none;
}

.event-item.status-completed,
.event-item.status-canceled,
.event-item.status-booked {
    background: none;
}

/* List View */
.list-view {
    display: none;
}

.list-view.active {
    display: block;
}

.calendar-view {
    display: block;
}

.calendar-view.active {
    display: block;
}

.list-item {
    border-bottom: 1px solid #e9ecef;
    padding: 1rem;
    transition: background-color 0.2s;
}

.list-item:hover {
    background: #f8f9fa;
}

.list-item:last-child {
    border-bottom: none;
}

.list-date {
    background: #e9ecef;
    padding: 0.75rem 1rem;
    font-weight: 600;
    color: #495057;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.list-time {
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.list-patient {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.list-details {
    font-size: 0.875rem;
    color: #6c757d;
}

.status-badge {
    padding: 0;
    border-radius: 3px;
    font-size: 0.75rem;
    font-weight: 500;
    width: 10px;
    height: 10px;
    display: inline-block;
    flex-shrink: 0;
    margin-right: 5px;
    /* Add margin to the right to separate from text */
}

/* Use the background colors from the old .event-item.status-* rules */
.status-badge.reserved {
    background: #fdc10d;
    /* Blue */
}
.status-badge.confirmed {
    background: #0d6efd;
    /* Blue */
}
.status-badge.completed {
    background: #198754;
    /* Green */
}

.status-badge.canceled {
    background: #dc3545;
    /* Red */
}

/* Loading Spinner */
.loading-spinner {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 3rem;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #0d6efd;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .calendar-header {
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }

    .calendar-nav {
        order: 2;
    }

    .calendar-title {
        order: 1;
        font-size: 1.25rem;
    }

    .view-toggle {
        order: 3;
    }

    .calendar-day {
        min-height: 80px;
        padding: 0.25rem;
    }

    .day-number {
        font-size: 0.75rem;
    }

    .event-item {
        font-size: 0.625rem;
        padding: 1px 4px;
    }

    .calendar-day-header {
        padding: 0.5rem 0.25rem;
        font-size: 0.75rem;
    }

    .room-slot-container {
        min-height: 30px;
        font-size: 0.65rem;
    }

    .room-badge {
        font-size: 0.55rem;
        padding: 1px 3px;
    }

    .patient-name {
        font-size: 0.6rem;
    }

    .graft-count {
        font-size: 0.55rem;
    }

    .technician-names {
        font-size: 0.5rem;
    }

    .agency-name {
        font-size: 0.5rem;
    }

    .add-surgery-text {
        font-size: 0.55rem;
    }
                                .not-available-text {
                                    font-size: 0.55rem;
                                }
}

@media (max-width: 576px) {
    .calendar-grid {
        font-size: 0.75rem;
    }

    .calendar-day {
        min-height: 60px;
    }

    .view-toggle button {
        padding: 0.375rem 0.5rem;
        font-size: 0.75rem;
    }

    .calendar-nav button {
        /* padding: 0.375rem 0.5rem; */
        font-size: 0.875rem;
    }

    .room-slot-container {
        min-height: 25px;
        font-size: 0.6rem;
    }

    .room-badge {
        font-size: 0.5rem;
        padding: 0px 2px;
    }

    .patient-name {
        font-size: 0.55rem;
    }

    .graft-count {
        font-size: 0.5rem;
    }

    .technician-names {
        font-size: 0.45rem;
    }

    .agency-name {
        font-size: 0.45rem;
    }

    .add-surgery-text {
        font-size: 0.5rem;
    }
        .not-available-text {
            font-size: 0.5rem;
        }
}